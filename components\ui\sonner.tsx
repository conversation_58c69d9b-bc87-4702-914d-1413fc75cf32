"use client";

import { Toaster as Son<PERSON>, ToasterProps } from "sonner";

const Toaster = ({ ...props }: ToasterProps) => {
  return (
    <Sonner
      theme={props.theme}
      className="toaster group"
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)",
        } as React.CSSProperties
      }
      richColors={true}
      position="bottom-center"
      //closeButton
      {...props}
    />
  );
};

export { Toaster };
