import { WordLists } from "./usernameGenerator";

export const wordLists: WordLists = {
    adjectives: [
        // Positive adjectives
        'amazing', 'brilliant', 'clever', 'dazzling', 'elegant', 'fantastic',
        'gorgeous', 'happy', 'incredible', 'joyful', 'kind', 'lovely',
        'magnificent', 'nice', 'outstanding', 'perfect', 'quiet', 'radiant',
        'stunning', 'terrific', 'unique', 'vibrant', 'wonderful', 'excellent',
        'zealous', 'bold', 'brave', 'calm', 'creative', 'dynamic', 'energetic',
        'fierce', 'gentle', 'humble', 'inspiring', 'jolly', 'keen', 'lively',
        'mighty', 'noble', 'optimistic', 'passionate', 'quirky', 'resilient',
        'serene', 'talented', 'upbeat', 'vivid', 'wise', 'youthful', 'zesty',

        // Descriptive adjectives
        'ancient', 'modern', 'vintage', 'classic', 'futuristic', 'timeless',
        'eternal', 'temporary', 'permanent', 'fleeting', 'lasting', 'enduring',
        'swift', 'slow', 'rapid', 'gradual', 'instant', 'delayed', 'prompt',
        'immediate', 'quick', 'speedy', 'sluggish', 'hasty', 'deliberate',
        'careful', 'reckless', 'cautious', 'wild', 'tame', 'fierce', 'gentle',
        'rough', 'smooth', 'sharp', 'dull', 'bright', 'dim', 'clear', 'cloudy',
        'transparent', 'opaque', 'visible', 'hidden', 'obvious', 'subtle',
        'simple', 'complex', 'easy', 'difficult', 'hard', 'soft', 'tender',
        'tough', 'strong', 'weak', 'powerful', 'fragile', 'sturdy', 'solid',
        'liquid', 'frozen', 'melted', 'crystalline', 'glassy', 'metallic',

        // Emotional adjectives
        'cheerful', 'gloomy', 'melancholy', 'ecstatic', 'depressed', 'elated',
        'anxious', 'relaxed', 'tense', 'peaceful', 'agitated', 'tranquil',
        'excited', 'bored', 'interested', 'curious', 'indifferent', 'eager',
        'reluctant', 'willing', 'hesitant', 'confident', 'doubtful', 'certain',
        'optimistic', 'pessimistic', 'hopeful', 'hopeless', 'grateful', 'bitter',
        'sweet', 'sour', 'spicy', 'bland', 'tasty', 'delicious', 'appetizing',
        'disgusting', 'revolting', 'appealing', 'attractive', 'repulsive',
        'charming', 'annoying', 'pleasant', 'unpleasant', 'comfortable',
        'uncomfortable', 'cozy', 'spacious', 'cramped', 'roomy', 'narrow',
        'wide', 'broad', 'slim', 'thick', 'thin', 'fat', 'skinny', 'plump',

        // Size and appearance
        'tiny', 'huge', 'enormous', 'gigantic', 'microscopic', 'colossal',
        'miniature', 'massive', 'petite', 'giant', 'small', 'large', 'medium',
        'average', 'normal', 'unusual', 'strange', 'odd', 'weird', 'bizarre',
        'extraordinary', 'ordinary', 'common', 'rare', 'unique', 'typical',
        'special', 'regular', 'irregular', 'symmetrical', 'asymmetrical',
        'balanced', 'unbalanced', 'proportional', 'disproportional', 'neat',
        'messy', 'tidy', 'cluttered', 'organized', 'chaotic', 'orderly',
        'random', 'systematic', 'methodical', 'haphazard', 'precise', 'vague',
        'exact', 'approximate', 'accurate', 'inaccurate', 'correct', 'wrong',
        'right', 'left', 'central', 'peripheral', 'inner', 'outer', 'internal',
        'external', 'domestic', 'foreign', 'native', 'exotic', 'local', 'global'
    ],

    nouns: [
        // Nature and landscape
        'adventure', 'bridge', 'castle', 'dream', 'eagle', 'forest', 'galaxy',
        'harmony', 'island', 'journey', 'kingdom', 'legend', 'mountain',
        'ocean', 'phoenix', 'quest', 'river', 'storm', 'treasure', 'universe',
        'valley', 'warrior', 'xenon', 'yacht', 'zenith', 'beacon', 'crystal',
        'dragon', 'ember', 'falcon', 'garden', 'horizon', 'infinity', 'jewel',
        'knight', 'labyrinth', 'meteor', 'nebula', 'oracle', 'palace', 'quantum',
        'rainbow', 'shadow', 'titan', 'utopia', 'vortex', 'whisper', 'xerus',
        'desert', 'tundra', 'prairie', 'meadow', 'field', 'grove', 'woods',
        'jungle', 'rainforest', 'savanna', 'plateau', 'canyon', 'cliff', 'peak',
        'summit', 'ridge', 'slope', 'hillside', 'foothill', 'lowland', 'highland',
        'marsh', 'swamp', 'bog', 'wetland', 'pond', 'stream', 'brook', 'creek',
        'waterfall', 'cascade', 'rapids', 'delta', 'estuary', 'bay', 'cove',
        'inlet', 'strait', 'channel', 'fjord', 'archipelago', 'atoll', 'reef',

        // Objects and items
        'anchor', 'arrow', 'blade', 'compass', 'dagger', 'envelope', 'feather',
        'globe', 'hammer', 'instrument', 'jar', 'key', 'lamp', 'mirror',
        'needle', 'orb', 'pendant', 'quill', 'rope', 'scroll', 'telescope',
        'umbrella', 'vase', 'wheel', 'xylophone', 'yoke', 'zipper', 'basket',
        'bottle', 'box', 'bucket', 'candle', 'chain', 'clock', 'coin', 'crown',
        'cup', 'dice', 'drum', 'flute', 'gem', 'glass', 'hook', 'idol',
        'journal', 'knife', 'lantern', 'mask', 'net', 'oar', 'pillow', 'ring',
        'shield', 'sword', 'torch', 'urn', 'violin', 'wand', 'book', 'map',

        // Abstract concepts
        'ability', 'balance', 'chance', 'destiny', 'energy', 'freedom', 'glory',
        'honor', 'imagination', 'justice', 'knowledge', 'liberty', 'mystery',
        'nobility', 'opportunity', 'passion', 'quality', 'reality', 'spirit',
        'truth', 'unity', 'vision', 'wisdom', 'youth', 'zeal', 'ambition',
        'beauty', 'courage', 'dignity', 'elegance', 'faith', 'grace', 'hope',
        'inspiration', 'joy', 'kindness', 'love', 'mercy', 'peace', 'strength',
        'virtue', 'wonder', 'achievement', 'success', 'victory', 'triumph',
        'power', 'force', 'might', 'will', 'desire', 'dream', 'goal', 'purpose',

        // Professions and roles
        'architect', 'artist', 'astronaut', 'captain', 'chef', 'detective',
        'doctor', 'engineer', 'explorer', 'farmer', 'guardian', 'hunter',
        'inventor', 'judge', 'keeper', 'leader', 'merchant', 'navigator',
        'officer', 'pilot', 'ranger', 'scholar', 'teacher', 'traveler',
        'builder', 'craftsman', 'designer', 'healer', 'musician', 'painter',
        'poet', 'scientist', 'soldier', 'student', 'writer', 'blacksmith',
        'carpenter', 'fisherman', 'gardener', 'librarian', 'messenger',
        'philosopher', 'shepherd', 'storyteller', 'watchman', 'wizard',

        // Structures and places
        'academy', 'arena', 'cathedral', 'citadel', 'colosseum', 'fortress',
        'gallery', 'harbor', 'laboratory', 'library', 'mansion', 'monastery',
        'observatory', 'pavilion', 'sanctuary', 'stadium', 'temple', 'theater',
        'tower', 'villa', 'workshop', 'bazaar', 'market', 'square', 'plaza',
        'courtyard', 'balcony', 'terrace', 'veranda', 'portico', 'alcove',
        'chamber', 'corridor', 'hallway', 'stairway', 'cellar', 'attic',
        'basement', 'rooftop', 'entrance', 'gateway', 'portal', 'threshold',

        // Mythical and fantasy
        'chimera', 'cyclops', 'gargoyle', 'griffon', 'hydra', 'kraken',
        'leviathan', 'minotaur', 'pegasus', 'sphinx', 'unicorn', 'basilisk',
        'centaur', 'dryad', 'elf', 'fairy', 'giant', 'goblin', 'nymph',
        'ogre', 'pixie', 'sprite', 'troll', 'vampire', 'werewolf', 'banshee',
        'demon', 'ghost', 'phantom', 'specter', 'wraith', 'angel', 'seraph',
        'cherub', 'deity', 'goddess', 'immortal', 'mortal', 'legend', 'myth',
        'folklore', 'tale', 'saga', 'epic', 'ballad', 'chronicle', 'fable'
    ],

    verbs: [
        // Action verbs
        'create', 'explore', 'discover', 'build', 'dance', 'sing', 'write',
        'paint', 'run', 'jump', 'fly', 'swim', 'climb', 'race', 'hunt',
        'gather', 'craft', 'design', 'invent', 'solve', 'learn', 'teach',
        'inspire', 'lead', 'follow', 'help', 'share', 'give', 'receive',
        'grow', 'bloom', 'shine', 'glow', 'sparkle', 'twinkle', 'flash',
        'burst', 'soar', 'dive', 'leap', 'bounce', 'roll', 'slide',

        // Movement verbs
        'walk', 'march', 'stride', 'stroll', 'wander', 'roam', 'travel',
        'journey', 'venture', 'advance', 'retreat', 'approach', 'depart',
        'arrive', 'return', 'ascend', 'descend', 'rise', 'fall', 'drop',
        'lift', 'raise', 'lower', 'push', 'pull', 'drag', 'carry', 'transport',
        'move', 'shift', 'transfer', 'migrate', 'navigate', 'steer', 'guide',
        'direct', 'point', 'aim', 'target', 'focus', 'concentrate', 'center',

        // Creative verbs
        'imagine', 'visualize', 'dream', 'envision', 'picture', 'sketch',
        'draw', 'illustrate', 'color', 'shade', 'sculpt', 'carve', 'mold',
        'shape', 'form', 'fashion', 'compose', 'arrange', 'organize', 'plan',
        'prepare', 'develop', 'evolve', 'transform', 'change', 'modify',
        'adapt', 'adjust', 'alter', 'improve', 'enhance', 'upgrade', 'refine',
        'perfect', 'polish', 'finish', 'complete', 'accomplish', 'achieve',

        // Communication verbs
        'speak', 'talk', 'communicate', 'express', 'articulate', 'voice',
        'announce', 'declare', 'proclaim', 'state', 'mention', 'remark',
        'comment', 'observe', 'note', 'notice', 'recognize', 'identify',
        'distinguish', 'differentiate', 'compare', 'contrast', 'analyze',
        'examine', 'investigate', 'research', 'study', 'review', 'evaluate',
        'assess', 'judge', 'criticize', 'praise', 'compliment', 'encourage',
        'support', 'assist', 'aid', 'rescue', 'save', 'protect', 'defend',
        'guard', 'watch', 'observe', 'monitor', 'supervise', 'oversee',

        // Emotional verbs
        'love', 'adore', 'cherish', 'treasure', 'value', 'appreciate',
        'admire', 'respect', 'honor', 'celebrate', 'rejoice', 'delight',
        'enjoy', 'relish', 'savor', 'embrace', 'welcome', 'greet', 'meet',
        'encounter', 'confront', 'face', 'challenge', 'dare', 'risk',
        'gamble', 'bet', 'wager', 'compete', 'contest', 'fight', 'battle',
        'struggle', 'strive', 'endeavor', 'attempt', 'try', 'experiment',
        'test', 'verify', 'confirm', 'validate', 'prove', 'demonstrate',
        'show', 'reveal', 'expose', 'uncover', 'unveil', 'disclose'
    ],

    colors: [
        // Basic colors
        'red', 'blue', 'green', 'yellow', 'orange', 'purple', 'pink',
        'black', 'white', 'gray', 'brown', 'silver', 'gold', 'crimson',
        'azure', 'emerald', 'amber', 'violet', 'indigo', 'turquoise',
        'maroon', 'navy', 'olive', 'teal', 'lime', 'magenta', 'cyan',
        'coral', 'salmon', 'khaki', 'plum', 'orchid', 'lavender', 'mint',

        // Extended colors
        'scarlet', 'ruby', 'cherry', 'rose', 'burgundy', 'wine', 'brick',
        'rust', 'copper', 'bronze', 'brass', 'champagne', 'beige', 'tan',
        'sand', 'cream', 'ivory', 'pearl', 'snow', 'charcoal', 'slate',
        'steel', 'pewter', 'platinum', 'titanium'
    ],

    animals: [
        // Mammals
        'lion', 'tiger', 'bear', 'wolf', 'fox', 'deer', 'rabbit', 'elephant',
        'giraffe', 'zebra', 'horse', 'cat', 'dog', 'panda', 'koala',
        'kangaroo', 'sloth', 'monkey', 'gorilla', 'chimpanzee', 'orangutan',
        'leopard', 'cheetah', 'jaguar', 'lynx', 'cougar', 'panther', 'bobcat',
        'hyena', 'jackal', 'coyote', 'dingo', 'badger', 'otter', 'beaver',
        'squirrel', 'chipmunk', 'hamster', 'mouse', 'rat', 'guinea pig',
        'hedgehog', 'porcupine', 'skunk', 'raccoon', 'opossum', 'ferret',
        'weasel', 'mink', 'sable', 'ermine', 'stoat', 'marten', 'wolverine',
        'seal', 'walrus', 'manatee', 'dugong', 'whale', 'dolphin', 'porpoise',
        'narwhal', 'beluga', 'orca', 'sperm whale', 'blue whale', 'humpback',

        // Birds
        'eagle', 'hawk', 'owl', 'falcon', 'kestrel', 'buzzard', 'vulture',
        'condor', 'albatross', 'pelican', 'crane', 'heron', 'stork', 'ibis',
        'flamingo', 'swan', 'goose', 'duck', 'mallard', 'teal', 'widgeon',
        'penguin', 'puffin', 'gannet', 'cormorant', 'kingfisher', 'woodpecker',
        'cardinal', 'bluejay', 'robin', 'sparrow', 'finch', 'canary', 'parakeet',
        'parrot', 'macaw', 'cockatoo', 'toucan', 'hornbill', 'peacock',
        'pheasant', 'quail', 'partridge', 'grouse', 'turkey', 'chicken',
        'rooster', 'hen', 'pigeon', 'dove', 'raven', 'crow', 'magpie',
        'nightingale', 'lark', 'thrush', 'blackbird', 'wren', 'warbler',

        // Reptiles and amphibians
        'snake', 'cobra', 'viper', 'python', 'boa', 'rattlesnake', 'mamba',
        'turtle', 'tortoise', 'terrapin', 'lizard', 'gecko', 'iguana',
        'chameleon', 'monitor', 'komodo', 'alligator', 'crocodile', 'caiman',
        'frog', 'toad', 'tadpole', 'salamander', 'newt', 'axolotl',

        // Sea creatures
        'shark', 'ray', 'skate', 'barracuda', 'tuna', 'salmon', 'trout',
        'bass', 'pike', 'carp', 'catfish', 'eel', 'moray', 'grouper',
        'snapper', 'flounder', 'sole', 'halibut', 'cod', 'haddock',
        'octopus', 'squid', 'cuttlefish', 'nautilus', 'jellyfish', 'anemone',
        'starfish', 'sea urchin', 'crab', 'lobster', 'shrimp', 'prawn',
        'krill', 'barnacle', 'mussel', 'oyster', 'clam', 'scallop',
        'seahorse', 'pipefish', 'angelfish', 'clownfish', 'butterflyfish',

        // Insects and arachnids
        'butterfly', 'moth', 'bee', 'wasp', 'hornet', 'ant', 'termite',
        'beetle', 'ladybug', 'firefly', 'dragonfly', 'damselfly', 'cricket',
        'grasshopper', 'locust', 'mantis', 'stick insect', 'cockroach',
        'spider', 'tarantula', 'scorpion', 'tick', 'mite', 'centipede',
        'millipede', 'slug', 'snail', 'worm', 'caterpillar', 'grub'
    ],

    tech: [
        // Computing terms
        'code', 'data', 'pixel', 'byte', 'cloud', 'server', 'network',
        'digital', 'cyber', 'tech', 'robot', 'android', 'chrome', 'firefox',
        'linux', 'binary', 'algorithm', 'matrix', 'vector', 'neural',
        'quantum', 'laser', 'radar', 'sensor', 'circuit', 'silicon',
        'processor', 'memory', 'storage', 'cache', 'kernel', 'protocol',
        'interface', 'database', 'framework', 'library', 'compiler',
        'interpreter', 'debugger', 'runtime', 'virtual', 'simulation',
        'encryption', 'firewall', 'antivirus', 'malware', 'spyware',
        'trojan', 'virus', 'worm', 'phishing', 'hacking', 'cracking',
        'penetration', 'vulnerability', 'exploit', 'patch', 'update',
        'upgrade', 'downgrade', 'backup', 'restore', 'recovery', 'clone',
        'mirror', 'synchronize', 'parallel', 'concurrent', 'thread',
        'process', 'daemon', 'service', 'application', 'software',
        'hardware', 'firmware', 'middleware', 'driver', 'plugin',
        'extension', 'module', 'component', 'package', 'bundle',
        'archive', 'compression', 'decompression', 'format', 'codec',
        'streaming', 'buffering', 'caching', 'indexing', 'searching',
        'sorting', 'filtering', 'parsing', 'rendering', 'compilation',
        'optimization', 'profiling', 'benchmarking', 'testing', 'debugging',
        'deployment', 'integration', 'automation', 'orchestration',
        'containerization', 'virtualization', 'emulation', 'abstraction',
        'encapsulation', 'inheritance', 'polymorphism', 'recursion',
        'iteration', 'loop', 'condition', 'branch', 'merge', 'commit'
    ],

    nature: [
        // Weather and natural phenomena
        'storm', 'rain', 'snow', 'wind', 'thunder', 'lightning', 'sun',
        'moon', 'star', 'planet', 'comet', 'meteor', 'aurora', 'dawn',
        'dusk', 'twilight', 'sunrise', 'sunset', 'rainbow', 'mist', 'fog',
        'dew', 'frost', 'hail', 'sleet', 'blizzard', 'hurricane', 'tornado',
        'cyclone', 'typhoon', 'monsoon', 'drought', 'flood', 'tide',
        'current', 'wave', 'ripple', 'whirlpool', 'eddy', 'rapid',
        'cascade', 'geyser', 'hotspring', 'volcano', 'eruption', 'lava',
        'magma', 'ash', 'smoke', 'steam', 'vapor', 'cloud', 'cumulus'
    ],

    food: [
        // Fruits and vegetables
        'apple', 'banana', 'cherry', 'date', 'elderberry', 'fig', 'grape',
        'kiwi', 'lemon', 'mango', 'orange', 'peach', 'pear', 'plum',
        'strawberry', 'blueberry', 'raspberry', 'blackberry', 'cranberry',
        'pineapple', 'papaya', 'coconut', 'avocado', 'tomato', 'cucumber',
        'carrot', 'potato', 'onion', 'garlic', 'ginger', 'pepper', 'chili',
        'lettuce', 'spinach', 'kale', 'broccoli', 'cauliflower', 'cabbage',
        'celery', 'asparagus', 'artichoke', 'mushroom', 'bean', 'pea',
        'corn', 'rice', 'wheat', 'oats', 'barley', 'quinoa', 'pasta',
        'bread', 'cake', 'cookie', 'muffin', 'pie', 'tart', 'donut'
    ],
};


export const getAllWords = (): string[] => {
    return Object.values(wordLists).flat();
};

